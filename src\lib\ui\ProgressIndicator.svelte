<script lang="ts">
	import { P2 } from './typography';

	interface Props {
		current: number;
		total: number;
		status?: string;
		showPercentage?: boolean;
		estimatedTimeRemaining?: number;
		variant?: 'default' | 'compact';
	}

	let { current, total, status = '', showPercentage = true, estimatedTimeRemaining, variant = 'default' }: Props = $props();

	let percentage = $derived(total > 0 ? Math.round((current / total) * 100) : 0);
	let formattedTime = $derived(() => {
		if (!estimatedTimeRemaining) return '';
		if (estimatedTimeRemaining < 60) return `${Math.round(estimatedTimeRemaining)}s remaining`;
		const minutes = Math.floor(estimatedTimeRemaining / 60);
		const seconds = Math.round(estimatedTimeRemaining % 60);
		return `${minutes}m ${seconds}s remaining`;
	});
</script>

<div class="progress-container" class:compact={variant === 'compact'}>
	{#if variant === 'default'}
		<div class="progress-header">
			<P2 isBold={true}>{status || `Processing ${current} of ${total}...`}</P2>
			{#if showPercentage}
				<P2 --text-color="var(--charcoal)">{percentage}%</P2>
			{/if}
		</div>
	{/if}
	
	<div class="progress-bar-container">
		<div class="progress-bar">
			<div 
				class="progress-fill" 
				style="width: {percentage}%"
			></div>
		</div>
	</div>
	
	{#if variant === 'default'}
		<div class="progress-details">
			{#if estimatedTimeRemaining}
				<P2 --text-color="var(--charcoal)" --text-size="0.875rem">{formattedTime()}</P2>
			{/if}
			{#if current > 0}
				<P2 --text-color="var(--charcoal)" --text-size="0.875rem">{current}/{total} completed</P2>
			{/if}
		</div>
	{:else}
		<div class="compact-details">
			<P2 --text-size="0.75rem" --text-color="var(--charcoal)">{current}/{total} ({percentage}%)</P2>
		</div>
	{/if}
</div>

<style>
	.progress-container {
		width: 100%;
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.progress-container.compact {
		gap: 0.25rem;
	}

	.progress-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.progress-bar-container {
		width: 100%;
	}

	.progress-bar {
		width: 100%;
		height: 0.5rem;
		background-color: var(--light-sky-blue);
		border-radius: 0.25rem;
		border: 1px solid var(--pitch-black);
		overflow: hidden;
	}

	.progress-fill {
		height: 100%;
		background: linear-gradient(90deg, var(--sky-blue) 0%, var(--aquamarine) 100%);
		transition: width 0.3s ease-in-out;
		border-radius: 0.25rem 0 0 0.25rem;
	}

	.progress-details {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.compact-details {
		text-align: center;
	}

	@media (max-width: 768px) {
		.progress-details {
			flex-direction: column;
			gap: 0.25rem;
			align-items: center;
		}
	}
</style>